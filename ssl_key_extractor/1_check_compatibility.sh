#!/bin/bash

# 系统兼容性检查脚本
# 用于验证目标系统是否满足SSL密钥提取器的运行要求
# 
# 使用方法:
#   ./1_check_compatibility.sh

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

# 检查结果统计
TOTAL_CHECKS=0
PASSED_CHECKS=0
FAILED_CHECKS=0
WARNING_CHECKS=0

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${CYAN}[STEP]${NC} $1"
}

log_check() {
    echo -e "${BLUE}[CHECK]${NC} $1"
}

# 记录检查结果
record_result() {
    local status=$1
    local message=$2
    
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
    
    case $status in
        "PASS")
            PASSED_CHECKS=$((PASSED_CHECKS + 1))
            echo -e "  ${GREEN}✓${NC} $message"
            ;;
        "FAIL")
            FAILED_CHECKS=$((FAILED_CHECKS + 1))
            echo -e "  ${RED}✗${NC} $message"
            ;;
        "WARN")
            WARNING_CHECKS=$((WARNING_CHECKS + 1))
            echo -e "  ${YELLOW}⚠${NC} $message"
            ;;
    esac
}

# 检查操作系统
check_os() {
    log_step "检查操作系统兼容性..."
    
    if [[ -f /etc/os-release ]]; then
        source /etc/os-release
        log_check "操作系统: $PRETTY_NAME"
        record_result "PASS" "操作系统信息获取成功"
        
        # 检查是否为支持的发行版
        case $ID in
            ubuntu|debian|centos|rhel|fedora|rocky|almalinux)
                record_result "PASS" "支持的Linux发行版: $ID"
                ;;
            *)
                record_result "WARN" "未测试的Linux发行版: $ID，可能需要额外配置"
                ;;
        esac
    else
        record_result "FAIL" "无法获取操作系统信息"
    fi
    
    # 检查内核版本
    local kernel_version=$(uname -r)
    log_check "内核版本: $kernel_version"
    record_result "PASS" "内核版本获取成功"
    
    # 检查架构
    local arch=$(uname -m)
    log_check "系统架构: $arch"
    if [[ "$arch" == "x86_64" ]]; then
        record_result "PASS" "支持的系统架构: $arch"
    else
        record_result "WARN" "未测试的系统架构: $arch，可能需要重新编译"
    fi
}

# 检查权限
check_permissions() {
    log_step "检查权限要求..."
    
    if [[ $EUID -eq 0 ]]; then
        record_result "PASS" "当前以root权限运行"
    else
        record_result "FAIL" "需要root权限运行，请使用: sudo $0"
    fi
    
    # 检查sudo权限
    if command -v sudo &> /dev/null; then
        record_result "PASS" "sudo命令可用"
    else
        record_result "WARN" "sudo命令不可用，可能影响某些操作"
    fi
}

# 检查必需的命令和工具
check_required_commands() {
    log_step "检查必需的命令和工具..."
    
    local required_commands=("tcpdump" "gcc" "make")
    local optional_commands=("curl" "wget" "tar" "gzip")
    
    # 检查必需命令
    for cmd in "${required_commands[@]}"; do
        if command -v "$cmd" &> /dev/null; then
            local version_info=""
            case $cmd in
                tcpdump)
                    version_info=$(tcpdump --version 2>&1 | head -1 || echo "")
                    ;;
                gcc)
                    version_info=$(gcc --version 2>&1 | head -1 || echo "")
                    ;;
                make)
                    version_info=$(make --version 2>&1 | head -1 || echo "")
                    ;;
            esac
            record_result "PASS" "$cmd 已安装 ($version_info)"
        else
            record_result "FAIL" "$cmd 未安装，请安装: yum install -y $cmd 或 apt install -y $cmd"
        fi
    done
    
    # 检查可选命令
    for cmd in "${optional_commands[@]}"; do
        if command -v "$cmd" &> /dev/null; then
            record_result "PASS" "$cmd 已安装 (可选)"
        else
            record_result "WARN" "$cmd 未安装 (可选，建议安装)"
        fi
    done
}

# 检查开发库和头文件
check_development_libraries() {
    log_step "检查开发库和头文件..."
    
    # 检查OpenSSL开发库
    if pkg-config --exists openssl 2>/dev/null; then
        local ssl_version=$(pkg-config --modversion openssl)
        record_result "PASS" "OpenSSL开发库已安装: $ssl_version"
    elif [[ -f /usr/include/openssl/ssl.h ]]; then
        record_result "PASS" "OpenSSL头文件存在"
    else
        record_result "FAIL" "OpenSSL开发库未安装，请安装: yum install -y openssl-devel 或 apt install -y libssl-dev"
    fi
    
    # 检查基本的C库头文件
    if [[ -f /usr/include/stdio.h ]]; then
        record_result "PASS" "基本C库头文件存在"
    else
        record_result "FAIL" "基本C库头文件缺失，请安装开发工具包"
    fi
}

# 检查网络配置
check_network() {
    log_step "检查网络配置..."
    
    # 检查网络接口
    local interfaces=$(ip link show | grep -E "^[0-9]+:" | awk '{print $2}' | tr -d ':' | grep -v lo)
    if [[ -n "$interfaces" ]]; then
        record_result "PASS" "网络接口可用: $(echo $interfaces | tr '\n' ' ')"
    else
        record_result "WARN" "未检测到活跃的网络接口"
    fi
    
    # 检查默认路由
    if ip route | grep -q default; then
        local default_iface=$(ip route | grep default | awk '{print $5}' | head -1)
        record_result "PASS" "默认路由存在，接口: $default_iface"
    else
        record_result "WARN" "未检测到默认路由"
    fi
    
    # 测试网络连通性
    if timeout 5 ping -c 1 ******* &>/dev/null; then
        record_result "PASS" "网络连通性正常"
    else
        record_result "WARN" "网络连通性测试失败，可能影响在线测试"
    fi
}

# 检查现有的SSL注入器
check_existing_injector() {
    log_step "检查现有的SSL注入器..."
    
    # 检查预编译的库文件
    if [[ -f "./libkeylog_injector.so" ]]; then
        if file ./libkeylog_injector.so | grep -q "ELF 64-bit"; then
            record_result "PASS" "预编译的SSL注入器库存在且格式正确"
            
            # 检查库依赖
            if ldd ./libkeylog_injector.so &>/dev/null; then
                record_result "PASS" "SSL注入器库依赖检查通过"
            else
                record_result "FAIL" "SSL注入器库依赖检查失败，可能需要重新编译"
            fi
        else
            record_result "FAIL" "SSL注入器库格式不正确"
        fi
    else
        record_result "WARN" "预编译的SSL注入器库不存在，需要编译"
    fi
    
    # 检查源码文件
    if [[ -f "./src/keylog_injector.c" ]]; then
        record_result "PASS" "SSL注入器源码存在"
    else
        record_result "WARN" "SSL注入器源码不存在"
    fi
    
    # 检查Makefile
    if [[ -f "./Makefile" ]]; then
        record_result "PASS" "Makefile存在"
    else
        record_result "WARN" "Makefile不存在"
    fi
}

# 检查系统资源
check_system_resources() {
    log_step "检查系统资源..."
    
    # 检查磁盘空间
    local available_space=$(df . | tail -1 | awk '{print $4}')
    local available_mb=$((available_space / 1024))
    
    if [[ $available_mb -gt 100 ]]; then
        record_result "PASS" "磁盘空间充足: ${available_mb}MB 可用"
    else
        record_result "WARN" "磁盘空间不足: ${available_mb}MB 可用，建议至少100MB"
    fi
    
    # 检查内存
    local total_mem=$(free -m | grep "Mem:" | awk '{print $2}')
    local available_mem=$(free -m | grep "Mem:" | awk '{print $7}')
    
    if [[ $available_mem -gt 100 ]]; then
        record_result "PASS" "内存充足: ${available_mem}MB 可用 / ${total_mem}MB 总计"
    else
        record_result "WARN" "可用内存较少: ${available_mem}MB 可用"
    fi
}

# 检查安全设置
check_security_settings() {
    log_step "检查安全设置..."
    
    # 检查SELinux
    if command -v getenforce &> /dev/null; then
        local selinux_status=$(getenforce 2>/dev/null || echo "Unknown")
        case $selinux_status in
            "Enforcing")
                record_result "WARN" "SELinux处于强制模式，可能需要额外配置"
                ;;
            "Permissive")
                record_result "PASS" "SELinux处于宽松模式"
                ;;
            "Disabled")
                record_result "PASS" "SELinux已禁用"
                ;;
            *)
                record_result "WARN" "SELinux状态未知: $selinux_status"
                ;;
        esac
    else
        record_result "PASS" "SELinux不存在或未安装"
    fi
    
    # 检查AppArmor
    if command -v aa-status &> /dev/null; then
        if aa-status --enabled 2>/dev/null; then
            record_result "WARN" "AppArmor已启用，可能需要额外配置"
        else
            record_result "PASS" "AppArmor未启用"
        fi
    else
        record_result "PASS" "AppArmor不存在或未安装"
    fi
}

# 生成兼容性报告
generate_report() {
    echo ""
    echo -e "${BLUE}=== 系统兼容性检查报告 ===${NC}"
    echo ""
    echo -e "${GREEN}检查统计:${NC}"
    echo "  总检查项: $TOTAL_CHECKS"
    echo "  通过: $PASSED_CHECKS"
    echo "  警告: $WARNING_CHECKS"
    echo "  失败: $FAILED_CHECKS"
    echo ""
    
    if [[ $FAILED_CHECKS -eq 0 ]]; then
        if [[ $WARNING_CHECKS -eq 0 ]]; then
            echo -e "${GREEN}✅ 系统完全兼容，可以直接部署！${NC}"
            return 0
        else
            echo -e "${YELLOW}⚠️ 系统基本兼容，但有警告项需要注意${NC}"
            echo ""
            echo -e "${YELLOW}建议操作:${NC}"
            echo "• 查看上述警告信息并根据需要进行配置"
            echo "• 可以继续部署，但建议先在测试环境验证"
            return 1
        fi
    else
        echo -e "${RED}❌ 系统不兼容，需要解决以下问题:${NC}"
        echo ""
        echo -e "${RED}必须解决的问题:${NC}"
        echo "• 查看上述失败项并安装缺失的依赖"
        echo "• 解决权限问题"
        echo "• 安装必需的开发工具和库"
        echo ""
        echo -e "${YELLOW}解决问题后请重新运行此脚本${NC}"
        return 2
    fi
}

# 显示帮助信息
show_help() {
    echo -e "${BLUE}系统兼容性检查脚本${NC}"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help    显示此帮助信息"
    echo ""
    echo "功能:"
    echo "• 检查操作系统兼容性"
    echo "• 验证必需的命令和工具"
    echo "• 检查开发库和头文件"
    echo "• 验证网络配置"
    echo "• 检查现有的SSL注入器"
    echo "• 评估系统资源"
    echo "• 检查安全设置"
    echo ""
    echo "返回值:"
    echo "  0 - 完全兼容"
    echo "  1 - 基本兼容但有警告"
    echo "  2 - 不兼容，需要解决问题"
}

# 主函数
main() {
    # 处理命令行参数
    case "${1:-}" in
        -h|--help)
            show_help
            exit 0
            ;;
        "")
            # 继续执行检查
            ;;
        *)
            log_error "未知参数: $1"
            show_help
            exit 1
            ;;
    esac
    
    echo -e "${BLUE}=== SSL密钥提取器系统兼容性检查 ===${NC}"
    echo ""
    echo "检查时间: $(date)"
    echo "检查主机: $(hostname)"
    echo ""
    
    # 执行所有检查
    check_os
    check_permissions
    check_required_commands
    check_development_libraries
    check_network
    check_existing_injector
    check_system_resources
    check_security_settings
    
    # 生成报告
    generate_report
    local exit_code=$?
    
    echo ""
    echo -e "${CYAN}下一步操作:${NC}"
    if [[ $exit_code -eq 0 ]]; then
        echo "1. 运行: ./2_setup_injection.sh"
        echo "2. 重启相关服务 (如nginx)"
        echo "3. 运行: ./3_capture_traffic.sh [时间]"
        echo "4. 运行: ./4_cleanup_injection.sh"
    elif [[ $exit_code -eq 1 ]]; then
        echo "1. 查看并处理警告项"
        echo "2. 运行: ./2_setup_injection.sh"
        echo "3. 在测试环境验证功能"
    else
        echo "1. 解决上述失败的检查项"
        echo "2. 重新运行此脚本"
    fi
    
    exit $exit_code
}

# 运行主函数
main "$@"
