#!/bin/bash

# 创建预编译部署包 - 包含编译好的二进制文件
# 适用于客户现场无法编译的环境

set -e

# 配置
PACKAGE_NAME="https_capture_precompiled"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
PACKAGE_DIR="${PACKAGE_NAME}_${TIMESTAMP}"
PACKAGE_FILE="${PACKAGE_DIR}.tar.gz"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${CYAN}[STEP]${NC} $1"
}

# 检查预编译文件
check_precompiled() {
    log_step "检查预编译文件..."
    
    if [[ ! -f "libkeylog_injector.so" ]]; then
        log_error "预编译的SSL注入器库不存在"
        log_info "请先运行: make"
        exit 1
    fi
    
    # 检查库文件
    if ! file libkeylog_injector.so | grep -q "ELF 64-bit"; then
        log_error "SSL注入器库格式不正确"
        exit 1
    fi
    
    # 检查依赖
    local missing_deps=()
    if ! ldd libkeylog_injector.so &>/dev/null; then
        log_warn "无法检查库依赖，可能在目标系统上不兼容"
    fi
    
    log_info "预编译文件检查通过"
}

# 创建打包目录
create_package_dir() {
    log_step "创建打包目录..."
    
    rm -rf "$PACKAGE_DIR" 2>/dev/null || true
    mkdir -p "$PACKAGE_DIR"
    mkdir -p "$PACKAGE_DIR/src"
    mkdir -p "$PACKAGE_DIR/docs"
    
    log_info "打包目录: $PACKAGE_DIR"
}

# 复制预编译文件
copy_precompiled_files() {
    log_step "复制预编译文件..."
    
    # 复制预编译的库文件
    cp libkeylog_injector.so "$PACKAGE_DIR/"
    log_info "复制SSL注入器库: libkeylog_injector.so"
    
    # 复制脚本文件
    cp portable_capture.sh "$PACKAGE_DIR/"
    cp setup_global_injection.sh "$PACKAGE_DIR/"
    
    # 复制源码（备用）
    cp src/keylog_injector.c "$PACKAGE_DIR/src/"
    cp Makefile "$PACKAGE_DIR/"
    
    # 复制文档
    cp README.md "$PACKAGE_DIR/docs/" 2>/dev/null || true
    cp LICENSE "$PACKAGE_DIR/docs/" 2>/dev/null || true
    
    log_info "文件复制完成"
}

# 创建简化的部署脚本
create_simple_deploy() {
    log_step "创建简化部署脚本..."
    
    cat > "$PACKAGE_DIR/deploy.sh" << 'EOF'
#!/bin/bash

# 预编译版本部署脚本 - 无需编译环境

set -e

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
RED='\033[0;31m'
NC='\033[0m'

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查权限
if [[ $EUID -ne 0 ]]; then
    log_error "需要root权限运行"
    echo "请使用: sudo $0"
    exit 1
fi

echo -e "${BLUE}=== HTTPS流量抓包工具 - 预编译版本部署 ===${NC}"
echo ""

# 检查tcpdump
if ! command -v tcpdump &> /dev/null; then
    log_warn "tcpdump未安装，正在尝试安装..."
    
    if command -v yum &> /dev/null; then
        yum install -y tcpdump
    elif command -v apt &> /dev/null; then
        apt update && apt install -y tcpdump
    elif command -v dnf &> /dev/null; then
        dnf install -y tcpdump
    else
        log_error "无法自动安装tcpdump，请手动安装"
        exit 1
    fi
fi

# 检查预编译库
if [[ ! -f "libkeylog_injector.so" ]]; then
    log_error "预编译库文件不存在"
    exit 1
fi

# 验证库文件
if ! ldd libkeylog_injector.so &>/dev/null; then
    log_error "预编译库与当前系统不兼容"
    log_info "系统信息: $(uname -a)"
    log_info "请在相同系统环境下重新编译"
    exit 1
fi

# 设置执行权限
chmod +x *.sh

log_info "部署完成！"
echo ""
echo -e "${GREEN}使用方法:${NC}"
echo "  sudo ./portable_capture.sh [时间]     # 开始抓包"
echo "  sudo ./portable_capture.sh 120       # 抓包2分钟"
echo ""
echo -e "${YELLOW}注意: 预编译版本只支持相同的系统环境${NC}"
EOF

    chmod +x "$PACKAGE_DIR/deploy.sh"
    log_info "简化部署脚本创建完成"
}

# 创建系统兼容性检查脚本
create_compatibility_check() {
    log_step "创建兼容性检查脚本..."
    
    cat > "$PACKAGE_DIR/check_compatibility.sh" << 'EOF'
#!/bin/bash

# 系统兼容性检查脚本

echo "=== 系统兼容性检查 ==="
echo ""

echo "操作系统:"
cat /etc/os-release 2>/dev/null || echo "无法获取OS信息"
echo ""

echo "内核版本:"
uname -a
echo ""

echo "架构信息:"
arch
echo ""

echo "库文件检查:"
if [[ -f "libkeylog_injector.so" ]]; then
    echo "SSL注入器库: 存在"
    file libkeylog_injector.so
    echo ""
    
    echo "依赖检查:"
    if ldd libkeylog_injector.so; then
        echo "✓ 库文件兼容"
    else
        echo "✗ 库文件不兼容"
        exit 1
    fi
else
    echo "✗ SSL注入器库不存在"
    exit 1
fi

echo ""
echo "tcpdump检查:"
if command -v tcpdump &> /dev/null; then
    echo "✓ tcpdump 已安装"
    tcpdump --version 2>&1 | head -1
else
    echo "✗ tcpdump 未安装"
fi

echo ""
echo "=== 兼容性检查完成 ==="
EOF

    chmod +x "$PACKAGE_DIR/check_compatibility.sh"
    log_info "兼容性检查脚本创建完成"
}

# 创建使用说明
create_precompiled_guide() {
    log_step "创建使用说明..."
    
    cat > "$PACKAGE_DIR/PRECOMPILED_GUIDE.md" << EOF
# HTTPS流量抓包工具 - 预编译版本

## 🚀 快速部署

### 1. 上传到目标服务器
\`\`\`bash
scp ${PACKAGE_FILE} user@target-server:/tmp/
\`\`\`

### 2. 解压并检查兼容性
\`\`\`bash
cd /tmp
tar -xzf ${PACKAGE_FILE}
cd ${PACKAGE_DIR}/

# 检查系统兼容性
./check_compatibility.sh
\`\`\`

### 3. 部署（如果兼容性检查通过）
\`\`\`bash
sudo ./deploy.sh
\`\`\`

### 4. 开始抓包
\`\`\`bash
sudo ./portable_capture.sh 60    # 抓包60秒
sudo ./portable_capture.sh 300   # 抓包5分钟
\`\`\`

## 📋 系统要求

### 编译环境信息
- **操作系统**: $(cat /etc/os-release | grep PRETTY_NAME | cut -d'"' -f2)
- **内核版本**: $(uname -r)
- **架构**: $(arch)
- **编译时间**: $(date)

### 依赖库版本
$(ldd libkeylog_injector.so 2>/dev/null | grep -E "(libssl|libcrypto|libc)" || echo "无法获取依赖信息")

### 兼容性说明
- ✅ **完全兼容**: 相同操作系统和版本
- ⚠️ **可能兼容**: 相同发行版的不同版本
- ❌ **不兼容**: 不同发行版或架构

## 🔧 故障排除

### 1. 库文件不兼容
\`\`\`bash
# 检查错误信息
ldd libkeylog_injector.so

# 如果提示缺少库文件，尝试安装
yum install -y openssl-libs  # CentOS/RHEL
apt install -y libssl1.1     # Ubuntu/Debian
\`\`\`

### 2. 权限问题
\`\`\`bash
# 确保使用root权限
sudo ./deploy.sh
sudo ./portable_capture.sh
\`\`\`

### 3. tcpdump缺失
\`\`\`bash
# 手动安装tcpdump
yum install -y tcpdump       # CentOS/RHEL
apt install -y tcpdump       # Ubuntu/Debian
\`\`\`

## 📦 备用方案

如果预编译版本不兼容，可以使用源码编译：

1. 安装编译环境：
   \`\`\`bash
   yum install -y gcc make openssl-devel  # CentOS/RHEL
   apt install -y gcc make libssl-dev     # Ubuntu/Debian
   \`\`\`

2. 编译：
   \`\`\`bash
   make clean && make
   \`\`\`

3. 重新打包：
   \`\`\`bash
   ./create_precompiled_package.sh
   \`\`\`

---
**注意**: 预编译版本仅适用于相同或兼容的系统环境
EOF

    log_info "使用说明创建完成"
}

# 记录编译环境信息
record_build_info() {
    log_step "记录编译环境信息..."
    
    cat > "$PACKAGE_DIR/BUILD_INFO.txt" << EOF
编译环境信息
============

编译时间: $(date)
编译主机: $(hostname)
操作系统: $(cat /etc/os-release | grep PRETTY_NAME | cut -d'"' -f2 2>/dev/null || echo "未知")
内核版本: $(uname -r)
架构: $(arch)

编译器信息:
----------
$(gcc --version 2>/dev/null | head -1 || echo "GCC: 未安装")

库文件信息:
----------
$(file libkeylog_injector.so)

依赖库:
------
$(ldd libkeylog_injector.so 2>/dev/null || echo "无法获取依赖信息")

OpenSSL版本:
-----------
$(openssl version 2>/dev/null || echo "OpenSSL: 未安装")

目标系统兼容性:
--------------
- 相同操作系统版本: 完全兼容
- 相同发行版不同版本: 可能兼容
- 不同发行版: 需要测试
- 不同架构: 不兼容
EOF

    log_info "编译环境信息记录完成"
}

# 打包文件
package_files() {
    log_step "打包文件..."
    
    tar -czf "$PACKAGE_FILE" "$PACKAGE_DIR"
    
    local size=$(ls -lh "$PACKAGE_FILE" | awk '{print $5}')
    local md5sum_output=$(md5sum "$PACKAGE_FILE" 2>/dev/null || md5 "$PACKAGE_FILE" 2>/dev/null || echo "N/A")
    
    log_info "打包完成: $PACKAGE_FILE"
    log_info "文件大小: $size"
    log_info "MD5校验: $md5sum_output"
}

# 显示部署说明
show_deployment_info() {
    echo ""
    echo -e "${BLUE}=== 预编译部署包创建完成 ===${NC}"
    echo ""
    echo -e "${GREEN}部署包信息:${NC}"
    echo -e "  文件名: ${CYAN}$PACKAGE_FILE${NC}"
    echo -e "  大小: ${CYAN}$(ls -lh "$PACKAGE_FILE" | awk '{print $5}')${NC}"
    echo -e "  编译环境: ${CYAN}$(cat /etc/os-release | grep PRETTY_NAME | cut -d'"' -f2 2>/dev/null)${NC}"
    echo ""
    echo -e "${GREEN}客户现场部署步骤:${NC}"
    echo -e "  1. 上传: ${CYAN}scp $PACKAGE_FILE user@server:/tmp/${NC}"
    echo -e "  2. 解压: ${CYAN}tar -xzf $PACKAGE_FILE${NC}"
    echo -e "  3. 进入: ${CYAN}cd $PACKAGE_DIR${NC}"
    echo -e "  4. 检查: ${CYAN}./check_compatibility.sh${NC}"
    echo -e "  5. 部署: ${CYAN}sudo ./deploy.sh${NC}"
    echo -e "  6. 抓包: ${CYAN}sudo ./portable_capture.sh [时间]${NC}"
    echo ""
    echo -e "${YELLOW}重要提醒:${NC}"
    echo "• 预编译版本只适用于相同或兼容的系统环境"
    echo "• 建议先在测试环境验证兼容性"
    echo "• 如不兼容，请使用源码编译版本"
}

# 主函数
main() {
    echo -e "${BLUE}=== 创建预编译部署包 ===${NC}"
    echo ""
    
    check_precompiled
    create_package_dir
    copy_precompiled_files
    create_simple_deploy
    create_compatibility_check
    create_precompiled_guide
    record_build_info
    package_files
    show_deployment_info
    
    # 清理临时目录
    rm -rf "$PACKAGE_DIR"
    
    echo -e "${GREEN}预编译部署包创建完成！${NC}"
}

# 运行主函数
main "$@"
